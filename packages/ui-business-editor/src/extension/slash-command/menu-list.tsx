import { cn } from '@repo/ui/lib/utils';
import { DropdownMenuItem } from '@repo/ui/components/ui/dropdown-menu';
import type { Editor } from '@tiptap/react';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import type { SuggestionKeyDownProps } from '../suggestion/suggestion';
import type { IMenuItem } from './constant';

export interface IMenuListProps {
  editor: Editor;
  items: IMenuItem[];
  onExit?: () => void;
  command: (props: IMenuItem) => void;
}

export const MenuList = forwardRef<
  { onKeyDown: (props: SuggestionKeyDownProps) => boolean },
  IMenuListProps
>(({ editor, items, onExit, command }, ref) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setSelectedIndex(items.length > 0 ? 0 : -1);
  }, [items]);

  // 优化的滚动逻辑：通过计算精确滚动距离
  useEffect(() => {
    if (selectedIndex >= 0 && selectedIndex < itemRefs.current.length) {
      const selectedElement = itemRefs.current[selectedIndex];
      const container = containerRef.current;

      if (selectedElement && container) {
        const containerRect = container.getBoundingClientRect();
        const elementRect = selectedElement.getBoundingClientRect();

        // 计算元素相对于容器的位置
        const elementTop = elementRect.top - containerRect.top + container.scrollTop;
        const elementBottom = elementTop + elementRect.height;

        // 容器的可视区域
        const containerTop = container.scrollTop;
        const containerBottom = containerTop + containerRect.height;

        // 设置边距，避免元素紧贴边缘
        const margin = 8;

        let targetScrollTop = container.scrollTop;

        // 如果元素在可视区域上方
        if (elementTop < containerTop + margin) {
          targetScrollTop = elementTop - margin;
        }
        // 如果元素在可视区域下方
        else if (elementBottom > containerBottom - margin) {
          targetScrollTop = elementBottom - containerRect.height + margin;
        }

        // 如果需要滚动，使用平滑滚动
        if (targetScrollTop !== container.scrollTop) {
          container.scrollTo({
            top: Math.max(0, targetScrollTop),
            behavior: 'smooth',
          });
        }
      }
    }
  }, [selectedIndex]);

  const selectItem = useCallback(
    (index: number) => {
      const item = items[index];
      if (item) {
        command(item);
      }
    },
    [editor, items],
  );

  const handleClick = (index: number) => {
    selectItem(index);
  };

  const handleMouseEnter = (index: number) => {
    setSelectedIndex(index);
  };

  // 实现键盘导航
  const onKeyDown = useCallback(
    (props: { event: KeyboardEvent }) => {
      const { event } = props;

      if (!items.length) {
        // 当没有匹配项时，退出 slash 面板
        onExit?.();
        return true;
      }

      // 更新选中状态的辅助函数
      const updateSelection = (newIndex: number) => {
        setSelectedIndex(newIndex);
        return true;
      };

      // 处理垂直布局中的移动
      const handleVerticalMovement = (direction: 'up' | 'down') => {
        let newIndex = selectedIndex;

        if (direction === 'up') {
          newIndex = selectedIndex - 1;
          if (newIndex < 0) {
            newIndex = items.length - 1;
          }
        } else {
          newIndex = selectedIndex + 1;
          if (newIndex >= items.length) {
            newIndex = 0;
          }
        }

        return updateSelection(newIndex);
      };

      // 按键处理策略
      const keyHandlers: Record<string, () => boolean> = {
        ArrowUp: () => {
          event.preventDefault();
          return handleVerticalMovement('up');
        },
        ArrowDown: () => {
          event.preventDefault();
          return handleVerticalMovement('down');
        },
        ArrowLeft: () => {
          event.preventDefault();
          return handleVerticalMovement('up');
        },
        ArrowRight: () => {
          event.preventDefault();
          return handleVerticalMovement('down');
        },
        Enter: () => {
          event.preventDefault();
          if (selectedIndex < 0 || selectedIndex >= items.length) {
            return false;
          }
          selectItem(selectedIndex);
          return true;
        },
        Tab: () => keyHandlers.Enter?.() ?? false,
      };

      return keyHandlers[event.key]?.() ?? false;
    },
    [items, selectedIndex, selectItem, onExit],
  );

  // 暴露 onKeyDown 方法给父组件
  useImperativeHandle(ref, () => ({
    onKeyDown,
  }));

  return (
    items.length > 0 && (
      <div
        ref={containerRef}
        className="rich-component mb-8 max-h-[14.4rem] min-w-56 flex-wrap overflow-y-scroll rounded-[inherit] bg-card p-2"
      >
        <div className="grid grid-cols-1 gap-0.5">
          {items.map((item: IMenuItem, index: number) => {
            const active = selectedIndex === index;

            return (
              <DropdownMenuItem
                key={item.name}
                onClick={() => handleClick(index)}
                onMouseEnter={() => handleMouseEnter(index)}
                ref={(el) => {
                  itemRefs.current[index] = el;
                }}
                className={cn(
                  'flex items-center justify-between cursor-pointer',
                  active && 'bg-accent text-accent-foreground',
                )}
              >
                <div className="flex gap-2 justify-start items-center">
                  {item.icon && item.icon}
                  {item.label && (
                    <div className="flex flex-col justify-start items-start">
                      <div>{item.label}</div>
                    </div>
                  )}
                </div>
                {item.markdownTip && (
                  <div className="footnote flex items-center justify-center text-[0.625rem] text-[rgba(70,68,64,0.45)]">
                    {item.markdownTip}
                  </div>
                )}
              </DropdownMenuItem>
            );
          })}
        </div>
      </div>
    )
  );
});

MenuList.displayName = 'MenuList';
